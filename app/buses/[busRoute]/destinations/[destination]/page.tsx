import { supabase } from '@/lib/supabase';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PageTransition } from '@/components/ui/page-transition';
import { notFound } from 'next/navigation';

interface Params { params: { busRoute: string; destination: string } }

export default async function BookingDetailsPage({ params }: Params) {
  const routeCode = decodeURIComponent(params.busRoute);
  const destinationName = decodeURIComponent(params.destination);

  const { data: bus } = await supabase
    .from('buses')
    .select('name, route_code')
    .eq('route_code', routeCode)
    .single();

  const { data: stop } = await supabase
    .from('route_stops')
    .select('stop_name, fare')
    .eq('route_code', routeCode)
    .eq('stop_name', destinationName)
    .single();

  if (!bus || !stop) return notFound();

  const price = stop.fare;

  return (
    <PageTransition>
      <div className="min-h-screen p-4">
        <div className="max-w-xl mx-auto">
          <Card>
            <CardContent className="p-6 space-y-4">
              <h1 className="text-2xl font-bold text-gray-900">Confirm Your Booking</h1>
              <div>
                <p className="text-gray-700">Bus: <span className="font-semibold">{bus.name}</span></p>
                <p className="text-gray-700">Route: <span className="font-semibold">{bus.route_code}</span></p>
                <p className="text-gray-700">Destination: <span className="font-semibold">{stop.stop_name}</span></p>
                <p className="text-gray-700">Price: <span className="font-semibold">₹{price}</span></p>
              </div>
              <Button className="w-full">Confirm and Make Payment</Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageTransition>
  );
} 