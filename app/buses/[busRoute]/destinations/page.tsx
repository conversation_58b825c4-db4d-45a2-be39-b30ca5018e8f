import { supabase } from '@/lib/supabase';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { PageTransition } from '@/components/ui/page-transition';
import { MapPin, ArrowRight } from 'lucide-react';
import Link from 'next/link';

interface Params {
  params: { busRoute: string };
}

export default async function DestinationsPage({ params }: Params) {
  const routeCode = decodeURIComponent(params.busRoute);

  const { data: bus } = await supabase
    .from('buses')
    .select('name, route_code, available_seats')
    .eq('route_code', routeCode)
    .eq('is_active', true)
    .single();

  const { data: stops } = await supabase
    .from('route_stops')
    .select('id, stop_name, fare, stop_order')
    .eq('route_code', routeCode)
    .eq('is_active', true)
    .order('stop_order');

  return (
    <PageTransition>
      <div className="min-h-screen p-4">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{bus?.name}</h1>
            <p className="text-gray-600">Route: {bus?.route_code}</p>
            <Badge variant="outline" className="mt-2">Available Seats: {bus?.available_seats ?? 0}</Badge>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {stops?.map((stop) => (
              <Link key={stop.id} href={`/buses/${routeCode}/destinations/${encodeURIComponent(stop.stop_name)}`}>
                <Card className="hover:bg-gray-50">
                  <CardContent className="p-4 flex items-center justify-between">
                    <div>
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4 text-red-600" />
                        <h3 className="font-semibold text-gray-800">{stop.stop_name}</h3>
                      </div>
                      <p className="text-sm text-gray-500">Fare: ₹{stop.fare}</p>
                    </div>
                    <ArrowRight className="w-4 h-4 text-gray-400" />
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </PageTransition>
  );
} 