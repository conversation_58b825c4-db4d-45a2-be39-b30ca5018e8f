import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import { supabaseAdmin } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      order_id,
      payment_id,
      signature,
      booking_id,
      bus_route,
    } = body as {
      order_id: string;
      payment_id: string;
      signature: string;
      booking_id: number;
      bus_route: string;
    };

    if (!order_id || !payment_id || !signature || !booking_id || !bus_route) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    const secret = process.env.RAZORPAY_KEY_SECRET as string;
    if (!secret) {
      return NextResponse.json({ error: 'Server misconfiguration' }, { status: 500 });
    }

    // Verify signature
    const hmacPayload = `${order_id}|${payment_id}`;
    const expectedSignature = crypto
      .createHmac('sha256', secret)
      .update(hmacPayload)
      .digest('hex');

    const isValid = expectedSignature === signature;

    // Update booking with payment info
    const { error: updateError } = await supabaseAdmin
      .from('bookings')
      .update({
        payment_status: isValid,
        razorpay_order_id: order_id,
        razorpay_payment_id: payment_id,
        razorpay_signature: signature,
      })
      .eq('id', booking_id);

    if (updateError) {
      return NextResponse.json({ error: 'Failed to update booking' }, { status: 500 });
    }

    if (isValid) {
      // Decrement seats on success
      const { data: decOk, error: decError } = await supabaseAdmin.rpc('decrease_buses_available_seats_guarded', {
        route: bus_route,
      });
      if (decError || decOk === false) {
        return NextResponse.json({ error: 'Seat decrement failed' }, { status: 500 });
      }
    }

    return NextResponse.json({ success: isValid });
  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.json({ error: 'Verification failed' }, { status: 500 });
  }
} 